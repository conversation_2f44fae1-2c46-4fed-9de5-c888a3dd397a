/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  line-height: 1.6;
}

/* App Container */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

/* Background Animation */
.app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: backgroundShift 8s ease-in-out infinite alternate;
  z-index: 0;
}

@keyframes backgroundShift {
  0% {
    transform: translateX(-10px) translateY(-10px);
  }
  100% {
    transform: translateX(10px) translateY(10px);
  }
}

/* Main Content */
.main-content {
  text-align: center;
  max-width: 900px;
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Glowing Title */
.glowing-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  margin: 0 0 2rem 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
  text-shadow:
    0 0 20px rgba(255, 107, 107, 0.5),
    0 0 40px rgba(78, 205, 196, 0.3),
    0 0 60px rgba(69, 183, 209, 0.2);
  position: relative;
  letter-spacing: 0.1em;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0% {
    filter: brightness(1) drop-shadow(0 0 20px rgba(255, 107, 107, 0.4));
  }
  100% {
    filter: brightness(1.2) drop-shadow(0 0 30px rgba(78, 205, 196, 0.6));
  }
}

/* Content Section */
.content-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  margin-top: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.content-section:hover {
  transform: translateY(-5px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Description Text */
.description {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #e0e0e0;
  margin: 0 0 2rem 0;
  text-align: left;
  font-weight: 300;
  letter-spacing: 0.02em;
}

/* Pipeline Concepts Section */
.concepts-section {
  margin-top: 2rem;
  text-align: left;
}

.concepts-title {
  font-size: 1.4rem;
  color: #4ecdc4;
  margin-bottom: 1rem;
  font-weight: 600;
}

.concepts-list {
  list-style: none;
  padding: 0;
}

.concept-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 0.8rem;
  color: #d0d0d0;
  font-size: 1rem;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.concept-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.3);
  transform: translateX(5px);
}

/* Application Information */
.app-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.info-label {
  font-weight: 500;
  color: #96ceb4;
  font-size: 0.9rem;
}

.info-value {
  color: #e0e0e0;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
}

/* Footer */
.app-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-text {
  color: #888;
  font-size: 0.9rem;
}

.footer-link {
  color: #4ecdc4;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #45b7d1;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .content-section {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .description {
    font-size: 1rem;
    line-height: 1.6;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
}

@media (max-width: 480px) {
  .content-section {
    padding: 1rem;
  }

  .description {
    font-size: 0.9rem;
    text-align: center;
  }

  .concepts-section {
    text-align: center;
  }

  .concept-item {
    text-align: left;
  }
}
