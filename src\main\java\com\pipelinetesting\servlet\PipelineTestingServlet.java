package com.pipelinetesting.servlet;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Main servlet for the Pipeline Testing Web Application.
 * Handles requests and serves the main page with pipeline testing content.
 */
@WebServlet(name = "PipelineTestingServlet", urlPatterns = {"/", "/home"})
public class PipelineTestingServlet extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Handles GET requests to display the main pipeline testing page.
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Set response content type
        response.setContentType("text/html;charset=UTF-8");
        
        // Prepare data for the JSP page
        preparePageData(request);
        
        // Forward to the main JSP page
        request.getRequestDispatcher("/index.jsp").forward(request, response);
    }
    
    /**
     * Handles POST requests (currently redirects to GET).
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // For now, redirect POST requests to GET
        response.sendRedirect(request.getContextPath() + "/");
    }
    
    /**
     * Prepares data to be displayed on the main page.
     * This method sets up all the dynamic content for the JSP.
     */
    private void preparePageData(HttpServletRequest request) {
        
        // Set the main title
        request.setAttribute("pageTitle", "Testing 15");
        
        // Set the main description content
        String description = "Pipeline testing is a critical practice in software development that involves " +
                           "automatically validating code changes through a series of automated tests and " +
                           "quality checks as they move through the development pipeline. This approach " +
                           "ensures that bugs are caught early, code quality is maintained, and deployments " +
                           "are reliable and consistent across different environments.";
        request.setAttribute("description", description);
        
        // Set current timestamp for cache busting and display
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        request.setAttribute("timestamp", timestamp);
        
        // Set application version
        request.setAttribute("appVersion", "1.0.0");
        
        // Set additional pipeline testing concepts
        String[] pipelineConcepts = {
            "Automated Quality Checks: Continuous validation of code quality and standards",
            "Build Validation: Automated build process with comprehensive error detection",
            "Test Automation: Unit, integration, and end-to-end testing in the pipeline",
            "Deployment Readiness: Ensuring applications are ready for production deployment",
            "Performance Monitoring: Tracking build performance and optimization metrics"
        };
        request.setAttribute("pipelineConcepts", pipelineConcepts);
        
        // Set environment information
        request.setAttribute("javaVersion", System.getProperty("java.version"));
        request.setAttribute("serverInfo", getServletContext().getServerInfo());
    }
    
    /**
     * Returns information about this servlet.
     */
    @Override
    public String getServletInfo() {
        return "Pipeline Testing Servlet - Demonstrates pipeline testing concepts in a Java web application";
    }
}
